/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fd2e0c2790d2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJmZDJlMGMyNzkwZDJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"Cognimosity\",\n    description: \"Learning platform\",\n    generator: 'v0.dev'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                defaultTheme: \"dark\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_3__.Toaster, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_cogni_ui__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/cogni-ui */ \"(rsc)/./components/cogni-ui.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cogni_ui__WEBPACK_IMPORTED_MODULE_1__.CogniUI, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\page.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBK0M7QUFFaEMsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELHlEQUFPQTs7Ozs7QUFDakIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXGFwcFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ29nbmlVSSB9IGZyb20gXCJAL2NvbXBvbmVudHMvY29nbmktdWlcIlxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcclxuICByZXR1cm4gPENvZ25pVUkgLz5cclxufVxyXG4iXSwibmFtZXMiOlsiQ29nbmlVSSIsIkhvbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/cogni-ui.tsx":
/*!*********************************!*\
  !*** ./components/cogni-ui.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CogniUI: () => (/* binding */ CogniUI)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const CogniUI = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CogniUI() from the server but CogniUI is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Documents\\cogni_api\\frontend\\components\\cogni-ui.tsx",
"CogniUI",
);

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   useTheme: () => (/* binding */ useTheme)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Documents\\cogni_api\\frontend\\components\\theme-provider.tsx",
"ThemeProvider",
);const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Documents\\cogni_api\\frontend\\components\\theme-provider.tsx",
"useTheme",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Csaand%5COneDrive%5CDocuments%5Ccogni_api%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csaand%5COneDrive%5CDocuments%5Ccogni_api%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Csaand%5COneDrive%5CDocuments%5Ccogni_api%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csaand%5COneDrive%5CDocuments%5Ccogni_api%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q3NhYW5kJTVDT25lRHJpdmUlNUNEb2N1bWVudHMlNUNjb2duaV9hcGklNUNmcm9udGVuZCU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDc2FhbmQlNUNPbmVEcml2ZSU1Q0RvY3VtZW50cyU1Q2NvZ25pX2FwaSU1Q2Zyb250ZW5kJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxzQkFBc0IsNElBQWdIO0FBQ3RJLHNCQUFzQiwwTkFBZ0Y7QUFDdEcsc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsZ09BQW1GO0FBQ3pHLG9CQUFvQix3SUFBOEc7QUFHaEk7QUFHQTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBR3JCO0FBQ0YsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBR0U7QUFDRjtBQUNPLHdCQUF3Qix1R0FBa0I7QUFDakQ7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgbW9kdWxlMCA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcc2FhbmRcXFxcT25lRHJpdmVcXFxcRG9jdW1lbnRzXFxcXGNvZ25pX2FwaVxcXFxmcm9udGVuZFxcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbmNvbnN0IG1vZHVsZTEgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIik7XG5jb25zdCBtb2R1bGUyID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlMyA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiKTtcbmNvbnN0IHBhZ2U0ID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxzYWFuZFxcXFxPbmVEcml2ZVxcXFxEb2N1bWVudHNcXFxcY29nbmlfYXBpXFxcXGZyb250ZW5kXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc3NyJ1xufTtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogW3BhZ2U0LCBcIkM6XFxcXFVzZXJzXFxcXHNhYW5kXFxcXE9uZURyaXZlXFxcXERvY3VtZW50c1xcXFxjb2duaV9hcGlcXFxcZnJvbnRlbmRcXFxcYXBwXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogW21vZHVsZTAsIFwiQzpcXFxcVXNlcnNcXFxcc2FhbmRcXFxcT25lRHJpdmVcXFxcRG9jdW1lbnRzXFxcXGNvZ25pX2FwaVxcXFxmcm9udGVuZFxcXFxhcHBcXFxcbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbbW9kdWxlMSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuJ2ZvcmJpZGRlbic6IFttb2R1bGUyLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIl0sXG4ndW5hdXRob3JpemVkJzogW21vZHVsZTMsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJDOlxcXFxVc2Vyc1xcXFxzYWFuZFxcXFxPbmVEcml2ZVxcXFxEb2N1bWVudHNcXFxcY29nbmlfYXBpXFxcXGZyb250ZW5kXFxcXGFwcFxcXFxwYWdlLnRzeFwiXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JhbEVycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogJycsXG4gICAgICAgIGZpbGVuYW1lOiAnJyxcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfVxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1wYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Csaand%5COneDrive%5CDocuments%5Ccogni_api%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csaand%5COneDrive%5CDocuments%5Ccogni_api%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(rsc)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(rsc)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Ccomponents%5C%5Ccogni-ui.tsx%22%2C%22ids%22%3A%5B%22CogniUI%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Ccomponents%5C%5Ccogni-ui.tsx%22%2C%22ids%22%3A%5B%22CogniUI%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/cogni-ui.tsx */ \"(rsc)/./components/cogni-ui.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NhYW5kJTVDJTVDT25lRHJpdmUlNUMlNUNEb2N1bWVudHMlNUMlNUNjb2duaV9hcGklNUMlNUNmcm9udGVuZCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjb2duaS11aS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJDb2duaVVJJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4SkFBc0oiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkNvZ25pVUlcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxzYWFuZFxcXFxPbmVEcml2ZVxcXFxEb2N1bWVudHNcXFxcY29nbmlfYXBpXFxcXGZyb250ZW5kXFxcXGNvbXBvbmVudHNcXFxcY29nbmktdWkudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Ccomponents%5C%5Ccogni-ui.tsx%22%2C%22ids%22%3A%5B%22CogniUI%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/cogni-ui.tsx":
/*!*********************************!*\
  !*** ./components/cogni-ui.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CogniUI: () => (/* binding */ CogniUI)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,CuboidIcon,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,CuboidIcon,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,CuboidIcon,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,CuboidIcon,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,CuboidIcon,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,CuboidIcon,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,CuboidIcon,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,CuboidIcon,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cuboid.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,CuboidIcon,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,CuboidIcon,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sidebar */ \"(ssr)/./components/sidebar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/theme-provider */ \"(ssr)/./components/theme-provider.tsx\");\n/* harmony import */ var _components_create_space_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/create-space-dialog */ \"(ssr)/./components/create-space-dialog.tsx\");\n/* harmony import */ var _components_upload_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/upload-modal */ \"(ssr)/./components/upload-modal.tsx\");\n/* harmony import */ var _components_paste_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/paste-modal */ \"(ssr)/./components/paste-modal.tsx\");\n/* harmony import */ var _components_record_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/record-modal */ \"(ssr)/./components/record-modal.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(ssr)/./components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ CogniUI auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CogniUI() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { theme, setTheme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createSpaceOpen, setCreateSpaceOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadModalOpen, setUploadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pasteModalOpen, setPasteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recordModalOpen, setRecordModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Mock user state - in a real app, this would come from your auth system\n    const [isLoggedIn, setIsLoggedIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleUpgradeClick = ()=>{\n        router.push(\"/subscription\");\n    };\n    const handleSignIn = ()=>{\n        router.push(\"/auth\");\n    };\n    const handleLogout = ()=>{\n        // Clear localStorage\n        localStorage.removeItem(\"isLoggedIn\");\n        localStorage.removeItem(\"username\");\n        localStorage.removeItem(\"token\");\n        setIsLoggedIn(false);\n        setUsername(\"\");\n        router.push(\"/\");\n    };\n    // Check auth state on mount\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"CogniUI.useEffect\": ()=>{\n            const userLoggedIn = localStorage.getItem(\"isLoggedIn\") === \"true\";\n            const storedUsername = localStorage.getItem(\"username\");\n            if (userLoggedIn && storedUsername) {\n                setIsLoggedIn(true);\n                setUsername(storedUsername);\n            }\n        }\n    }[\"CogniUI.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-background text-foreground overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                isOpen: sidebarOpen,\n                setIsOpen: setSidebarOpen,\n                isLoggedIn: isLoggedIn,\n                username: username,\n                onLogout: handleLogout\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"h-10 w-10 rounded-full\",\n                                onClick: ()=>setSidebarOpen(true),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-10 w-10 rounded-full\",\n                                        onClick: ()=>setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                        children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 65\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, this),\n                                    isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenu, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    className: \"bg-purple-600 hover:bg-purple-700\",\n                                                    children: username\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuContent, {\n                                                align: \"end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuItem, {\n                                                    onClick: handleLogout,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Log out\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-purple-600 hover:bg-purple-700\",\n                                        onClick: handleSignIn,\n                                        children: \"Sign In / Register\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-transparent border border-purple-500 text-purple-500 hover:bg-purple-500/10\",\n                                        onClick: handleUpgradeClick,\n                                        children: \"Upgrade\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-5xl mx-auto px-4 py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-16 w-16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        src: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png\",\n                                        alt: \"Cognimosity Logo\",\n                                        fill: true,\n                                        className: \"object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-center mb-12\",\n                                children: \"Ready to unlock something new today?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setUploadModalOpen(true),\n                                        className: `border rounded-lg p-6 flex flex-col items-center transition-all duration-300 hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)] group relative ${theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-2 right-2 bg-purple-500 text-xs px-2 py-0.5 rounded-full\",\n                                                children: \"Popular\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `mb-4 p-3 rounded-full transition-all duration-300 ${theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-900\"} group-hover:bg-purple-500/20`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: `h-6 w-6 group-hover:text-purple-500 transition-all duration-300 ${theme === \"light\" ? \"text-black\" : \"\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-1\",\n                                                children: \"Upload\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: `text-sm text-center ${theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-400\"}`,\n                                                children: \"PDF, PPT, DOC, TXT, AUDIO\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setPasteModalOpen(true),\n                                        className: `border rounded-lg p-6 flex flex-col items-center transition-all duration-300 hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)] group ${theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `mb-4 p-3 rounded-full transition-all duration-300 ${theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-900\"} group-hover:bg-purple-500/20`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: `h-6 w-6 group-hover:text-purple-500 transition-all duration-300 ${theme === \"light\" ? \"text-black\" : \"\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-1\",\n                                                children: \"Paste\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: `text-sm text-center ${theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-400\"}`,\n                                                children: \"YouTube, Website, Text\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setRecordModalOpen(true),\n                                        className: `border rounded-lg p-6 flex flex-col items-center transition-all duration-300 hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)] group ${theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `mb-4 p-3 rounded-full transition-all duration-300 ${theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-900\"} group-hover:bg-purple-500/20`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: `h-6 w-6 group-hover:text-purple-500 transition-all duration-300 ${theme === \"light\" ? \"text-black\" : \"\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-1\",\n                                                children: \"Record\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: `text-sm text-center ${theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-400\"}`,\n                                                children: \"Record Your Lecture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: \"My spaces\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-1 gap-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `border rounded-lg p-4 flex items-center justify-between hover:border-purple-500 transition-all duration-300 ${theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"My Space\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"h-8 w-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            className: `w-full justify-center gap-2 border-dashed py-6 hover:border-purple-500 transition-all duration-300 ${theme === \"light\" ? \"border-black\" : \"border-neutral-700\"}`,\n                                            onClick: ()=>setCreateSpaceOpen(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_CuboidIcon_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Add space\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold\",\n                                                children: \"Continue learning\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"link\",\n                                                className: \"text-neutral-400\",\n                                                children: \"View all\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `border rounded-lg overflow-hidden hover:border-purple-500 transition-all duration-300 ${theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-32 bg-purple-900 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl font-bold text-white\",\n                                                        children: \"MAP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `border rounded-lg overflow-hidden hover:border-purple-500 transition-all duration-300 ${theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `h-32 ${theme === \"light\" ? \"bg-gray-100\" : \"bg-neutral-900\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `border rounded-lg overflow-hidden hover:border-purple-500 transition-all duration-300 ${theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `h-32 ${theme === \"light\" ? \"bg-gray-100\" : \"bg-neutral-900\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_upload_modal__WEBPACK_IMPORTED_MODULE_8__.UploadModal, {\n                isOpen: uploadModalOpen,\n                setIsOpen: setUploadModalOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_paste_modal__WEBPACK_IMPORTED_MODULE_9__.PasteModal, {\n                isOpen: pasteModalOpen,\n                setIsOpen: setPasteModalOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_record_modal__WEBPACK_IMPORTED_MODULE_10__.RecordModal, {\n                isOpen: recordModalOpen,\n                setIsOpen: setRecordModalOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_create_space_dialog__WEBPACK_IMPORTED_MODULE_7__.CreateSpaceDialog, {\n                open: createSpaceOpen,\n                setOpen: setCreateSpaceOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/cogni-ui.tsx\n");

/***/ }),

/***/ "(ssr)/./components/create-space-dialog.tsx":
/*!********************************************!*\
  !*** ./components/create-space-dialog.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateSpaceDialog: () => (/* binding */ CreateSpaceDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(ssr)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./components/ui/label.tsx\");\n/* __next_internal_client_entry_do_not_use__ CreateSpaceDialog auto */ \n\n\n\n\n\nfunction CreateSpaceDialog({ open, setOpen }) {\n    const [spaceName, setSpaceName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleCreateSpace = ()=>{\n        if (spaceName.trim()) {\n            // Here you would typically save the new space to your backend\n            console.log(\"Creating space:\", spaceName);\n            setOpen(false);\n            setSpaceName(\"\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: open,\n        onOpenChange: setOpen,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"sm:max-w-md bg-background border-border\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                        children: \"Create New Space\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\create-space-dialog.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\create-space-dialog.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"name\",\n                                        children: \"Space Name\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\create-space-dialog.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        id: \"name\",\n                                        placeholder: \"Enter space name\",\n                                        value: spaceName,\n                                        onChange: (e)=>setSpaceName(e.target.value),\n                                        className: \"bg-background border-border\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\create-space-dialog.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\create-space-dialog.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setOpen(false),\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\create-space-dialog.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        className: \"bg-purple-600 hover:bg-purple-700\",\n                                        onClick: handleCreateSpace,\n                                        disabled: !spaceName.trim(),\n                                        children: \"Create Space\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\create-space-dialog.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\create-space-dialog.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\create-space-dialog.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\create-space-dialog.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\create-space-dialog.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\create-space-dialog.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/create-space-dialog.tsx\n");

/***/ }),

/***/ "(ssr)/./components/paste-modal.tsx":
/*!************************************!*\
  !*** ./components/paste-modal.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PasteModal: () => (/* binding */ PasteModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(ssr)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(ssr)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(ssr)/./components/ui/textarea.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ PasteModal auto */ \n\n\n\n\n\n\n\n\nfunction PasteModal({ isOpen, setIsOpen }) {\n    const [youtubeUrl, setYoutubeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [websiteUrl, setWebsiteUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [textContent, setTextContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"youtube\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const handleSubmit = ()=>{\n        // Store the content in localStorage to simulate persistence\n        if (activeTab === \"youtube\" && youtubeUrl) {\n            localStorage.setItem(\"pastedContent\", JSON.stringify({\n                type: \"youtube\",\n                url: youtubeUrl\n            }));\n        } else if (activeTab === \"website\" && websiteUrl) {\n            localStorage.setItem(\"pastedContent\", JSON.stringify({\n                type: \"website\",\n                url: websiteUrl\n            }));\n        } else if (activeTab === \"text\" && textContent) {\n            localStorage.setItem(\"pastedContent\", JSON.stringify({\n                type: \"text\",\n                content: textContent\n            }));\n        }\n        setIsOpen(false);\n        router.push(\"/process?type=paste\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: isOpen,\n        onOpenChange: setIsOpen,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"sm:max-w-md bg-neutral-900 border-neutral-800\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                        className: \"text-center\",\n                        children: \"Paste Content\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                    defaultValue: \"youtube\",\n                    className: \"mt-4\",\n                    onValueChange: setActiveTab,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                            className: \"grid grid-cols-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                    value: \"youtube\",\n                                    className: \"data-[state=active]:bg-purple-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Globe_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"YouTube\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                    value: \"website\",\n                                    className: \"data-[state=active]:bg-purple-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Globe_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Website\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                    value: \"text\",\n                                    className: \"data-[state=active]:bg-purple-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Globe_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Text\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                            value: \"youtube\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm text-neutral-400 mb-2 block\",\n                                            children: \"Paste YouTube URL\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            placeholder: \"https://www.youtube.com/watch?v=...\",\n                                            value: youtubeUrl,\n                                            onChange: (e)=>setYoutubeUrl(e.target.value),\n                                            className: \"bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-neutral-500\",\n                                    children: \"Enter a YouTube video URL to extract and analyze its content\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                            value: \"website\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm text-neutral-400 mb-2 block\",\n                                            children: \"Paste Website URL\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            placeholder: \"https://example.com\",\n                                            value: websiteUrl,\n                                            onChange: (e)=>setWebsiteUrl(e.target.value),\n                                            className: \"bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-neutral-500\",\n                                    children: \"Enter a website URL to extract and analyze its content\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                            value: \"text\",\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm text-neutral-400 mb-2 block\",\n                                        children: \"Paste Text Content\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                        placeholder: \"Paste or type your text here...\",\n                                        value: textContent,\n                                        onChange: (e)=>setTextContent(e.target.value),\n                                        className: \"min-h-[150px] bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 flex justify-end gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>setIsOpen(false),\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            className: \"bg-purple-600 hover:bg-purple-700\",\n                            onClick: handleSubmit,\n                            disabled: activeTab === \"youtube\" && !youtubeUrl || activeTab === \"website\" && !websiteUrl || activeTab === \"text\" && !textContent,\n                            children: \"Process Content\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\paste-modal.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Bhc3RlLW1vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFZ0M7QUFDeUQ7QUFDMUM7QUFDUTtBQUN3QjtBQUNsQztBQUNNO0FBQ1I7QUFPcEMsU0FBU2dCLFdBQVcsRUFBRUMsTUFBTSxFQUFFQyxTQUFTLEVBQW1CO0lBQy9ELE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHcEIsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDcUIsWUFBWUMsY0FBYyxHQUFHdEIsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDdUIsYUFBYUMsZUFBZSxHQUFHeEIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDeUIsV0FBV0MsYUFBYSxHQUFHMUIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTTJCLFNBQVNaLDBEQUFTQTtJQUV4QixNQUFNYSxlQUFlO1FBQ25CLDREQUE0RDtRQUM1RCxJQUFJSCxjQUFjLGFBQWFOLFlBQVk7WUFDekNVLGFBQWFDLE9BQU8sQ0FDbEIsaUJBQ0FDLEtBQUtDLFNBQVMsQ0FBQztnQkFDYkMsTUFBTTtnQkFDTkMsS0FBS2Y7WUFDUDtRQUVKLE9BQU8sSUFBSU0sY0FBYyxhQUFhSixZQUFZO1lBQ2hEUSxhQUFhQyxPQUFPLENBQ2xCLGlCQUNBQyxLQUFLQyxTQUFTLENBQUM7Z0JBQ2JDLE1BQU07Z0JBQ05DLEtBQUtiO1lBQ1A7UUFFSixPQUFPLElBQUlJLGNBQWMsVUFBVUYsYUFBYTtZQUM5Q00sYUFBYUMsT0FBTyxDQUNsQixpQkFDQUMsS0FBS0MsU0FBUyxDQUFDO2dCQUNiQyxNQUFNO2dCQUNORSxTQUFTWjtZQUNYO1FBRUo7UUFFQUwsVUFBVTtRQUNWUyxPQUFPUyxJQUFJLENBQUM7SUFDZDtJQUVBLHFCQUNFLDhEQUFDbkMseURBQU1BO1FBQUNvQyxNQUFNcEI7UUFBUXFCLGNBQWNwQjtrQkFDbEMsNEVBQUNoQixnRUFBYUE7WUFBQ3FDLFdBQVU7OzhCQUN2Qiw4REFBQ3BDLCtEQUFZQTs4QkFDWCw0RUFBQ0MsOERBQVdBO3dCQUFDbUMsV0FBVTtrQ0FBYzs7Ozs7Ozs7Ozs7OEJBR3ZDLDhEQUFDOUIscURBQUlBO29CQUFDK0IsY0FBYTtvQkFBVUQsV0FBVTtvQkFBT0UsZUFBZWY7O3NDQUMzRCw4REFBQ2YseURBQVFBOzRCQUFDNEIsV0FBVTs7OENBQ2xCLDhEQUFDM0IsNERBQVdBO29DQUFDOEIsT0FBTTtvQ0FBVUgsV0FBVTs7c0RBQ3JDLDhEQUFDakMsa0dBQU9BOzRDQUFDaUMsV0FBVTs7Ozs7O3dDQUFpQjs7Ozs7Ozs4Q0FHdEMsOERBQUMzQiw0REFBV0E7b0NBQUM4QixPQUFNO29DQUFVSCxXQUFVOztzREFDckMsOERBQUNoQyxrR0FBS0E7NENBQUNnQyxXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7OzhDQUdwQyw4REFBQzNCLDREQUFXQTtvQ0FBQzhCLE9BQU07b0NBQU9ILFdBQVU7O3NEQUNsQyw4REFBQy9CLG1HQUFRQTs0Q0FBQytCLFdBQVU7Ozs7Ozt3Q0FBaUI7Ozs7Ozs7Ozs7Ozs7c0NBS3pDLDhEQUFDN0IsNERBQVdBOzRCQUFDZ0MsT0FBTTs0QkFBVUgsV0FBVTs7OENBQ3JDLDhEQUFDSTs7c0RBQ0MsOERBQUNDOzRDQUFNTCxXQUFVO3NEQUFzQzs7Ozs7O3NEQUN2RCw4REFBQzFCLHVEQUFLQTs0Q0FDSmdDLGFBQVk7NENBQ1pILE9BQU92Qjs0Q0FDUDJCLFVBQVUsQ0FBQ0MsSUFBTTNCLGNBQWMyQixFQUFFQyxNQUFNLENBQUNOLEtBQUs7NENBQzdDSCxXQUFVOzs7Ozs7Ozs7Ozs7OENBR2QsOERBQUNVO29DQUFFVixXQUFVOzhDQUEyQjs7Ozs7Ozs7Ozs7O3NDQUcxQyw4REFBQzdCLDREQUFXQTs0QkFBQ2dDLE9BQU07NEJBQVVILFdBQVU7OzhDQUNyQyw4REFBQ0k7O3NEQUNDLDhEQUFDQzs0Q0FBTUwsV0FBVTtzREFBc0M7Ozs7OztzREFDdkQsOERBQUMxQix1REFBS0E7NENBQ0pnQyxhQUFZOzRDQUNaSCxPQUFPckI7NENBQ1B5QixVQUFVLENBQUNDLElBQU16QixjQUFjeUIsRUFBRUMsTUFBTSxDQUFDTixLQUFLOzRDQUM3Q0gsV0FBVTs7Ozs7Ozs7Ozs7OzhDQUdkLDhEQUFDVTtvQ0FBRVYsV0FBVTs4Q0FBMkI7Ozs7Ozs7Ozs7OztzQ0FHMUMsOERBQUM3Qiw0REFBV0E7NEJBQUNnQyxPQUFNOzRCQUFPSCxXQUFVO3NDQUNsQyw0RUFBQ0k7O2tEQUNDLDhEQUFDQzt3Q0FBTUwsV0FBVTtrREFBc0M7Ozs7OztrREFDdkQsOERBQUN6Qiw2REFBUUE7d0NBQ1ArQixhQUFZO3dDQUNaSCxPQUFPbkI7d0NBQ1B1QixVQUFVLENBQUNDLElBQU12QixlQUFldUIsRUFBRUMsTUFBTSxDQUFDTixLQUFLO3dDQUM5Q0gsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTWxCLDhEQUFDSTtvQkFBSUosV0FBVTs7c0NBQ2IsOERBQUNsQyx5REFBTUE7NEJBQUM2QyxTQUFROzRCQUFVQyxTQUFTLElBQU1qQyxVQUFVO3NDQUFROzs7Ozs7c0NBRzNELDhEQUFDYix5REFBTUE7NEJBQ0xrQyxXQUFVOzRCQUNWWSxTQUFTdkI7NEJBQ1R3QixVQUNFLGNBQWUsYUFBYSxDQUFDakMsY0FDNUJNLGNBQWMsYUFBYSxDQUFDSixjQUM1QkksY0FBYyxVQUFVLENBQUNGO3NDQUU3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPWCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcY29tcG9uZW50c1xccGFzdGUtbW9kYWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXHJcblxyXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiXHJcbmltcG9ydCB7IERpYWxvZywgRGlhbG9nQ29udGVudCwgRGlhbG9nSGVhZGVyLCBEaWFsb2dUaXRsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvZGlhbG9nXCJcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIlxyXG5pbXBvcnQgeyBZb3V0dWJlLCBHbG9iZSwgRmlsZVRleHQgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcclxuaW1wb3J0IHsgVGFicywgVGFic0NvbnRlbnQsIFRhYnNMaXN0LCBUYWJzVHJpZ2dlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvdGFic1wiXHJcbmltcG9ydCB7IElucHV0IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9pbnB1dFwiXHJcbmltcG9ydCB7IFRleHRhcmVhIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90ZXh0YXJlYVwiXHJcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIlxyXG5cclxuaW50ZXJmYWNlIFBhc3RlTW9kYWxQcm9wcyB7XHJcbiAgaXNPcGVuOiBib29sZWFuXHJcbiAgc2V0SXNPcGVuOiAoaXNPcGVuOiBib29sZWFuKSA9PiB2b2lkXHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBQYXN0ZU1vZGFsKHsgaXNPcGVuLCBzZXRJc09wZW4gfTogUGFzdGVNb2RhbFByb3BzKSB7XHJcbiAgY29uc3QgW3lvdXR1YmVVcmwsIHNldFlvdXR1YmVVcmxdID0gdXNlU3RhdGUoXCJcIilcclxuICBjb25zdCBbd2Vic2l0ZVVybCwgc2V0V2Vic2l0ZVVybF0gPSB1c2VTdGF0ZShcIlwiKVxyXG4gIGNvbnN0IFt0ZXh0Q29udGVudCwgc2V0VGV4dENvbnRlbnRdID0gdXNlU3RhdGUoXCJcIilcclxuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGUoXCJ5b3V0dWJlXCIpXHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcclxuXHJcbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gKCkgPT4ge1xyXG4gICAgLy8gU3RvcmUgdGhlIGNvbnRlbnQgaW4gbG9jYWxTdG9yYWdlIHRvIHNpbXVsYXRlIHBlcnNpc3RlbmNlXHJcbiAgICBpZiAoYWN0aXZlVGFiID09PSBcInlvdXR1YmVcIiAmJiB5b3V0dWJlVXJsKSB7XHJcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFxyXG4gICAgICAgIFwicGFzdGVkQ29udGVudFwiLFxyXG4gICAgICAgIEpTT04uc3RyaW5naWZ5KHtcclxuICAgICAgICAgIHR5cGU6IFwieW91dHViZVwiLFxyXG4gICAgICAgICAgdXJsOiB5b3V0dWJlVXJsLFxyXG4gICAgICAgIH0pLFxyXG4gICAgICApXHJcbiAgICB9IGVsc2UgaWYgKGFjdGl2ZVRhYiA9PT0gXCJ3ZWJzaXRlXCIgJiYgd2Vic2l0ZVVybCkge1xyXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcclxuICAgICAgICBcInBhc3RlZENvbnRlbnRcIixcclxuICAgICAgICBKU09OLnN0cmluZ2lmeSh7XHJcbiAgICAgICAgICB0eXBlOiBcIndlYnNpdGVcIixcclxuICAgICAgICAgIHVybDogd2Vic2l0ZVVybCxcclxuICAgICAgICB9KSxcclxuICAgICAgKVxyXG4gICAgfSBlbHNlIGlmIChhY3RpdmVUYWIgPT09IFwidGV4dFwiICYmIHRleHRDb250ZW50KSB7XHJcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFxyXG4gICAgICAgIFwicGFzdGVkQ29udGVudFwiLFxyXG4gICAgICAgIEpTT04uc3RyaW5naWZ5KHtcclxuICAgICAgICAgIHR5cGU6IFwidGV4dFwiLFxyXG4gICAgICAgICAgY29udGVudDogdGV4dENvbnRlbnQsXHJcbiAgICAgICAgfSksXHJcbiAgICAgIClcclxuICAgIH1cclxuXHJcbiAgICBzZXRJc09wZW4oZmFsc2UpXHJcbiAgICByb3V0ZXIucHVzaChcIi9wcm9jZXNzP3R5cGU9cGFzdGVcIilcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8RGlhbG9nIG9wZW49e2lzT3Blbn0gb25PcGVuQ2hhbmdlPXtzZXRJc09wZW59PlxyXG4gICAgICA8RGlhbG9nQ29udGVudCBjbGFzc05hbWU9XCJzbTptYXgtdy1tZCBiZy1uZXV0cmFsLTkwMCBib3JkZXItbmV1dHJhbC04MDBcIj5cclxuICAgICAgICA8RGlhbG9nSGVhZGVyPlxyXG4gICAgICAgICAgPERpYWxvZ1RpdGxlIGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+UGFzdGUgQ29udGVudDwvRGlhbG9nVGl0bGU+XHJcbiAgICAgICAgPC9EaWFsb2dIZWFkZXI+XHJcblxyXG4gICAgICAgIDxUYWJzIGRlZmF1bHRWYWx1ZT1cInlvdXR1YmVcIiBjbGFzc05hbWU9XCJtdC00XCIgb25WYWx1ZUNoYW5nZT17c2V0QWN0aXZlVGFifT5cclxuICAgICAgICAgIDxUYWJzTGlzdCBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0zIG1iLTRcIj5cclxuICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwieW91dHViZVwiIGNsYXNzTmFtZT1cImRhdGEtW3N0YXRlPWFjdGl2ZV06YmctcHVycGxlLTYwMFwiPlxyXG4gICAgICAgICAgICAgIDxZb3V0dWJlIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XHJcbiAgICAgICAgICAgICAgWW91VHViZVxyXG4gICAgICAgICAgICA8L1RhYnNUcmlnZ2VyPlxyXG4gICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJ3ZWJzaXRlXCIgY2xhc3NOYW1lPVwiZGF0YS1bc3RhdGU9YWN0aXZlXTpiZy1wdXJwbGUtNjAwXCI+XHJcbiAgICAgICAgICAgICAgPEdsb2JlIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XHJcbiAgICAgICAgICAgICAgV2Vic2l0ZVxyXG4gICAgICAgICAgICA8L1RhYnNUcmlnZ2VyPlxyXG4gICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJ0ZXh0XCIgY2xhc3NOYW1lPVwiZGF0YS1bc3RhdGU9YWN0aXZlXTpiZy1wdXJwbGUtNjAwXCI+XHJcbiAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XHJcbiAgICAgICAgICAgICAgVGV4dFxyXG4gICAgICAgICAgICA8L1RhYnNUcmlnZ2VyPlxyXG4gICAgICAgICAgPC9UYWJzTGlzdD5cclxuXHJcbiAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJ5b3V0dWJlXCIgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1uZXV0cmFsLTQwMCBtYi0yIGJsb2NrXCI+UGFzdGUgWW91VHViZSBVUkw8L2xhYmVsPlxyXG4gICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJodHRwczovL3d3dy55b3V0dWJlLmNvbS93YXRjaD92PS4uLlwiXHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17eW91dHViZVVybH1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0WW91dHViZVVybChlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1uZXV0cmFsLTgwMCBib3JkZXItbmV1dHJhbC03MDAgZm9jdXMtdmlzaWJsZTpyaW5nLXB1cnBsZS01MDBcIlxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbmV1dHJhbC01MDBcIj5FbnRlciBhIFlvdVR1YmUgdmlkZW8gVVJMIHRvIGV4dHJhY3QgYW5kIGFuYWx5emUgaXRzIGNvbnRlbnQ8L3A+XHJcbiAgICAgICAgICA8L1RhYnNDb250ZW50PlxyXG5cclxuICAgICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT1cIndlYnNpdGVcIiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW5ldXRyYWwtNDAwIG1iLTIgYmxvY2tcIj5QYXN0ZSBXZWJzaXRlIFVSTDwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImh0dHBzOi8vZXhhbXBsZS5jb21cIlxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e3dlYnNpdGVVcmx9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFdlYnNpdGVVcmwoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctbmV1dHJhbC04MDAgYm9yZGVyLW5ldXRyYWwtNzAwIGZvY3VzLXZpc2libGU6cmluZy1wdXJwbGUtNTAwXCJcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW5ldXRyYWwtNTAwXCI+RW50ZXIgYSB3ZWJzaXRlIFVSTCB0byBleHRyYWN0IGFuZCBhbmFseXplIGl0cyBjb250ZW50PC9wPlxyXG4gICAgICAgICAgPC9UYWJzQ29udGVudD5cclxuXHJcbiAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJ0ZXh0XCIgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1uZXV0cmFsLTQwMCBtYi0yIGJsb2NrXCI+UGFzdGUgVGV4dCBDb250ZW50PC9sYWJlbD5cclxuICAgICAgICAgICAgICA8VGV4dGFyZWFcclxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiUGFzdGUgb3IgdHlwZSB5b3VyIHRleHQgaGVyZS4uLlwiXHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17dGV4dENvbnRlbnR9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFRleHRDb250ZW50KGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1pbi1oLVsxNTBweF0gYmctbmV1dHJhbC04MDAgYm9yZGVyLW5ldXRyYWwtNzAwIGZvY3VzLXZpc2libGU6cmluZy1wdXJwbGUtNTAwXCJcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XHJcbiAgICAgICAgPC9UYWJzPlxyXG5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgZmxleCBqdXN0aWZ5LWVuZCBnYXAtMlwiPlxyXG4gICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIG9uQ2xpY2s9eygpID0+IHNldElzT3BlbihmYWxzZSl9PlxyXG4gICAgICAgICAgICBDYW5jZWxcclxuICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1wdXJwbGUtNjAwIGhvdmVyOmJnLXB1cnBsZS03MDBcIlxyXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTdWJtaXR9XHJcbiAgICAgICAgICAgIGRpc2FibGVkPXtcclxuICAgICAgICAgICAgICAoYWN0aXZlVGFiID09PSBcInlvdXR1YmVcIiAmJiAheW91dHViZVVybCkgfHxcclxuICAgICAgICAgICAgICAoYWN0aXZlVGFiID09PSBcIndlYnNpdGVcIiAmJiAhd2Vic2l0ZVVybCkgfHxcclxuICAgICAgICAgICAgICAoYWN0aXZlVGFiID09PSBcInRleHRcIiAmJiAhdGV4dENvbnRlbnQpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgUHJvY2VzcyBDb250ZW50XHJcbiAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9EaWFsb2dDb250ZW50PlxyXG4gICAgPC9EaWFsb2c+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIkRpYWxvZyIsIkRpYWxvZ0NvbnRlbnQiLCJEaWFsb2dIZWFkZXIiLCJEaWFsb2dUaXRsZSIsIkJ1dHRvbiIsIllvdXR1YmUiLCJHbG9iZSIsIkZpbGVUZXh0IiwiVGFicyIsIlRhYnNDb250ZW50IiwiVGFic0xpc3QiLCJUYWJzVHJpZ2dlciIsIklucHV0IiwiVGV4dGFyZWEiLCJ1c2VSb3V0ZXIiLCJQYXN0ZU1vZGFsIiwiaXNPcGVuIiwic2V0SXNPcGVuIiwieW91dHViZVVybCIsInNldFlvdXR1YmVVcmwiLCJ3ZWJzaXRlVXJsIiwic2V0V2Vic2l0ZVVybCIsInRleHRDb250ZW50Iiwic2V0VGV4dENvbnRlbnQiLCJhY3RpdmVUYWIiLCJzZXRBY3RpdmVUYWIiLCJyb3V0ZXIiLCJoYW5kbGVTdWJtaXQiLCJsb2NhbFN0b3JhZ2UiLCJzZXRJdGVtIiwiSlNPTiIsInN0cmluZ2lmeSIsInR5cGUiLCJ1cmwiLCJjb250ZW50IiwicHVzaCIsIm9wZW4iLCJvbk9wZW5DaGFuZ2UiLCJjbGFzc05hbWUiLCJkZWZhdWx0VmFsdWUiLCJvblZhbHVlQ2hhbmdlIiwidmFsdWUiLCJkaXYiLCJsYWJlbCIsInBsYWNlaG9sZGVyIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwicCIsInZhcmlhbnQiLCJvbkNsaWNrIiwiZGlzYWJsZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/paste-modal.tsx\n");

/***/ }),

/***/ "(ssr)/./components/record-modal.tsx":
/*!*************************************!*\
  !*** ./components/record-modal.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecordModal: () => (/* binding */ RecordModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(ssr)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Mic_Pause_Play_Save_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,Pause,Play,Save,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_Pause_Play_Save_Square_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,Pause,Play,Save,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_Pause_Play_Save_Square_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,Pause,Play,Save,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_Pause_Play_Save_Square_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,Pause,Play,Save,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_Pause_Play_Save_Square_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,Pause,Play,Save,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ RecordModal auto */ \n\n\n\n\n\n\nfunction RecordModal({ isOpen, setIsOpen }) {\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPaused, setIsPaused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recordingTime, setRecordingTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [audioURL, setAudioURL] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioChunksRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const timerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecordModal.useEffect\": ()=>{\n            return ({\n                \"RecordModal.useEffect\": ()=>{\n                    if (timerRef.current) {\n                        clearInterval(timerRef.current);\n                    }\n                    if (audioURL) {\n                        URL.revokeObjectURL(audioURL);\n                    }\n                }\n            })[\"RecordModal.useEffect\"];\n        }\n    }[\"RecordModal.useEffect\"], [\n        audioURL\n    ]);\n    const startRecording = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            const mediaRecorder = new MediaRecorder(stream);\n            mediaRecorderRef.current = mediaRecorder;\n            audioChunksRef.current = [];\n            mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0) {\n                    audioChunksRef.current.push(event.data);\n                }\n            };\n            mediaRecorder.onstop = ()=>{\n                const audioBlob = new Blob(audioChunksRef.current, {\n                    type: \"audio/wav\"\n                });\n                const url = URL.createObjectURL(audioBlob);\n                setAudioURL(url);\n            };\n            mediaRecorder.start();\n            setIsRecording(true);\n            setIsPaused(false);\n            // Start timer\n            timerRef.current = setInterval(()=>{\n                setRecordingTime((prev)=>prev + 1);\n            }, 1000);\n        } catch (error) {\n            console.error(\"Error accessing microphone:\", error);\n        }\n    };\n    const stopRecording = ()=>{\n        if (mediaRecorderRef.current && isRecording) {\n            mediaRecorderRef.current.stop();\n            setIsRecording(false);\n            // Stop timer\n            if (timerRef.current) {\n                clearInterval(timerRef.current);\n                timerRef.current = null;\n            }\n            // Stop all audio tracks\n            mediaRecorderRef.current.stream.getTracks().forEach((track)=>track.stop());\n        }\n    };\n    const pauseRecording = ()=>{\n        if (mediaRecorderRef.current && isRecording && !isPaused) {\n            mediaRecorderRef.current.pause();\n            setIsPaused(true);\n            // Pause timer\n            if (timerRef.current) {\n                clearInterval(timerRef.current);\n                timerRef.current = null;\n            }\n        }\n    };\n    const resumeRecording = ()=>{\n        if (mediaRecorderRef.current && isRecording && isPaused) {\n            mediaRecorderRef.current.resume();\n            setIsPaused(false);\n            // Resume timer\n            timerRef.current = setInterval(()=>{\n                setRecordingTime((prev)=>prev + 1);\n            }, 1000);\n        }\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins.toString().padStart(2, \"0\")}:${secs.toString().padStart(2, \"0\")}`;\n    };\n    const handleProcessRecording = ()=>{\n        if (audioURL) {\n            // Store recording info in localStorage to simulate persistence\n            localStorage.setItem(\"recordedAudio\", JSON.stringify({\n                duration: formatTime(recordingTime),\n                timestamp: new Date().toISOString()\n            }));\n        }\n        setIsOpen(false);\n        router.push(\"/process?type=record\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: isOpen,\n        onOpenChange: (open)=>{\n            if (!open && isRecording) {\n                stopRecording();\n            }\n            setIsOpen(open);\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"sm:max-w-md bg-neutral-900 border-neutral-800\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                        className: \"text-center\",\n                        children: \"Record Audio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 flex flex-col items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                scale: 1\n                            },\n                            animate: {\n                                scale: isRecording && !isPaused ? [\n                                    1,\n                                    1.1,\n                                    1\n                                ] : 1,\n                                transition: {\n                                    repeat: isRecording && !isPaused ? Number.POSITIVE_INFINITY : 0,\n                                    duration: 1.5\n                                }\n                            },\n                            className: \"relative mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `p-8 rounded-full ${isRecording ? \"bg-red-500/20\" : \"bg-neutral-800\"}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_Pause_Play_Save_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: `h-10 w-10 ${isRecording ? \"text-red-500\" : \"text-purple-500\"}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                isRecording && !isPaused && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    className: \"absolute inset-0 rounded-full border-2 border-red-500\",\n                                    initial: {\n                                        scale: 1,\n                                        opacity: 1\n                                    },\n                                    animate: {\n                                        scale: [\n                                            1,\n                                            1.2,\n                                            1.2,\n                                            1\n                                        ],\n                                        opacity: [\n                                            1,\n                                            0.8,\n                                            0.2,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Number.POSITIVE_INFINITY,\n                                        ease: \"easeOut\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-2xl font-mono mb-6\",\n                            children: formatTime(recordingTime)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-4 mb-6\",\n                            children: !isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: startRecording,\n                                className: \"bg-purple-600 hover:bg-purple-700 rounded-full h-12 w-12 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_Pause_Play_Save_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: stopRecording,\n                                        className: \"bg-red-600 hover:bg-red-700 rounded-full h-12 w-12 p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_Pause_Play_Save_Square_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, this),\n                                    isPaused ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: resumeRecording,\n                                        className: \"bg-purple-600 hover:bg-purple-700 rounded-full h-12 w-12 p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_Pause_Play_Save_Square_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: pauseRecording,\n                                        className: \"bg-purple-600 hover:bg-purple-700 rounded-full h-12 w-12 p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_Pause_Play_Save_Square_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this),\n                        audioURL && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                                    ref: audioRef,\n                                    src: audioURL,\n                                    controls: true,\n                                    className: \"w-full mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleProcessRecording,\n                                    className: \"w-full bg-purple-600 hover:bg-purple-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_Pause_Play_Save_Square_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Process Recording\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n            lineNumber: 143,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\record-modal.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/record-modal.tsx\n");

/***/ }),

/***/ "(ssr)/./components/sidebar.tsx":
/*!********************************!*\
  !*** ./components/sidebar.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cuboid.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/theme-provider */ \"(ssr)/./components/theme-provider.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n\nfunction Sidebar({ isOpen, setIsOpen, isLoggedIn = false, username = \"\", onLogout }) {\n    const { theme, setTheme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    // Determine text color based on theme\n    const textColor = theme === \"dark\" ? \"text-neutral-300\" : \"text-neutral-800\";\n    const iconColor = theme === \"dark\" ? \"text-neutral-400\" : \"text-neutral-600\";\n    const goToHome = ()=>{\n        router.push(\"/\");\n        setIsOpen(false);\n    };\n    const handleSignIn = ()=>{\n        router.push(\"/auth\");\n        setIsOpen(false);\n    };\n    const handleLogout = ()=>{\n        if (onLogout) {\n            onLogout();\n        }\n        setIsOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 0.5\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    className: \"fixed inset-0 bg-black z-40 md:hidden\",\n                    onClick: ()=>setIsOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    x: \"-100%\"\n                },\n                animate: {\n                    x: isOpen ? 0 : \"-100%\"\n                },\n                transition: {\n                    duration: 0.3,\n                    ease: \"easeInOut\"\n                },\n                className: `fixed top-0 left-0 h-full w-64 border-r flex flex-col z-50 ${theme === \"light\" ? \"border-black bg-white\" : \"border-border bg-background\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center gap-2 hover:text-purple-500\",\n                                onClick: goToHome,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-6 w-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png\",\n                                            alt: \"Cognimosity Logo\",\n                                            fill: true,\n                                            className: \"object-contain\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold\",\n                                        children: \"Cognimosity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"h-8 w-8\",\n                                onClick: ()=>setIsOpen(false),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 py-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"ghost\",\n                            className: `w-full justify-start px-4 py-2 text-sm ${textColor} hover:text-purple-500`,\n                            onClick: ()=>setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                            children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: `h-4 w-4 mr-2 ${iconColor}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Light Mode\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: `h-4 w-4 mr-2 ${iconColor}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Dark Mode\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 py-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `w-full px-4 py-2 ${textColor} font-medium`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"History\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pl-6 space-y-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-neutral-500 py-2\",\n                                    children: \"No recent history\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 py-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `w-full px-4 py-2 ${textColor} font-medium`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Spaces\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pl-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"ghost\",\n                                        className: `w-full justify-start px-4 py-2 text-sm ${textColor} hover:text-purple-500`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: `h-4 w-4 mr-2 ${iconColor}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"My Space\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"outline\",\n                                        className: `w-full justify-start gap-2 border-dashed border-neutral-700 text-sm ${textColor} hover:border-purple-500 hover:text-purple-500`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Add space\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 py-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `w-full px-4 py-2 ${textColor} font-medium`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Help & Tools\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pl-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"ghost\",\n                                        className: `w-full justify-start px-4 py-2 text-sm ${textColor} hover:text-purple-500`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: `h-4 w-4 mr-2 ${iconColor}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Feedback\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"ghost\",\n                                        className: `w-full justify-start px-4 py-2 text-sm ${textColor} hover:text-purple-500`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: `h-4 w-4 mr-2 ${iconColor}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Quick Guide\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"ghost\",\n                                        className: `w-full justify-start px-4 py-2 text-sm ${textColor} hover:text-purple-500`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: `h-4 w-4 mr-2 ${iconColor}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Invite & Earn\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-auto p-3 space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"outline\",\n                                className: `w-full justify-center text-purple-500 hover:bg-purple-500/10 ${theme === \"light\" ? \"border-purple-500\" : \"border-purple-500\"}`,\n                                children: \"Upgrade\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `rounded-md p-2 text-center ${theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-900\"}`,\n                                children: isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"ghost\",\n                                    className: `w-full justify-center text-sm ${textColor} hover:text-purple-500`,\n                                    onClick: handleLogout,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: `h-4 w-4 mr-2 ${iconColor}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Log out (\",\n                                        username,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"ghost\",\n                                    className: `w-full justify-center text-sm ${textColor} hover:text-purple-500`,\n                                    onClick: handleSignIn,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: `h-4 w-4 mr-2 ${iconColor}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Sign in / Sign up\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst initialState = {\n    theme: \"system\",\n    setTheme: ()=>null\n};\nconst ThemeProviderContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(initialState);\nfunction ThemeProvider({ children, defaultTheme = \"system\", storageKey = \"ui-theme\", enableSystem = true, disableTransitionOnChange = false, ...props }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTheme);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const root = window.document.documentElement;\n            const savedTheme = localStorage.getItem(storageKey);\n            if (savedTheme) {\n                setTheme(savedTheme);\n            } else if (enableSystem) {\n                const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n                setTheme(systemTheme);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        storageKey,\n        enableSystem\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const root = window.document.documentElement;\n            if (disableTransitionOnChange) {\n                root.classList.add(\"transition-none\");\n                window.setTimeout({\n                    \"ThemeProvider.useEffect\": ()=>{\n                        root.classList.remove(\"transition-none\");\n                    }\n                }[\"ThemeProvider.useEffect\"], 0);\n            }\n            if (theme === \"system\" && enableSystem) {\n                const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n                root.classList.remove(\"light\", \"dark\");\n                root.classList.add(systemTheme);\n                return;\n            }\n            root.classList.remove(\"light\", \"dark\");\n            root.classList.add(theme);\n            localStorage.setItem(storageKey, theme);\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme,\n        disableTransitionOnChange,\n        enableSystem,\n        storageKey\n    ]);\n    const value = {\n        theme,\n        setTheme: (theme)=>{\n            setTheme(theme);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeProviderContext.Provider, {\n        ...props,\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeProviderContext);\n    if (context === undefined) throw new Error(\"useTheme must be used within a ThemeProvider\");\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2J1dHRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUE4QjtBQUNhO0FBQ3NCO0FBRWpDO0FBRWhDLE1BQU1JLGlCQUFpQkYsNkRBQUdBLENBQ3hCLDRWQUNBO0lBQ0VHLFVBQVU7UUFDUkMsU0FBUztZQUNQQyxTQUFTO1lBQ1RDLGFBQ0U7WUFDRkMsU0FDRTtZQUNGQyxXQUNFO1lBQ0ZDLE9BQU87WUFDUEMsTUFBTTtRQUNSO1FBQ0FDLE1BQU07WUFDSk4sU0FBUztZQUNUTyxJQUFJO1lBQ0pDLElBQUk7WUFDSkMsTUFBTTtRQUNSO0lBQ0Y7SUFDQUMsaUJBQWlCO1FBQ2ZYLFNBQVM7UUFDVE8sTUFBTTtJQUNSO0FBQ0Y7QUFTRixNQUFNSyx1QkFBU2xCLDZDQUFnQixDQUM3QixDQUFDLEVBQUVvQixTQUFTLEVBQUVkLE9BQU8sRUFBRU8sSUFBSSxFQUFFUSxVQUFVLEtBQUssRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQ3hELE1BQU1DLE9BQU9ILFVBQVVwQixzREFBSUEsR0FBRztJQUM5QixxQkFDRSw4REFBQ3VCO1FBQ0NKLFdBQVdqQiw4Q0FBRUEsQ0FBQ0MsZUFBZTtZQUFFRTtZQUFTTztZQUFNTztRQUFVO1FBQ3hERyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE9BQU9PLFdBQVcsR0FBRztBQUVZIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxjb21wb25lbnRzXFx1aVxcYnV0dG9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxyXG5pbXBvcnQgeyBTbG90IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1zbG90XCJcclxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxyXG5cclxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxyXG5cclxuY29uc3QgYnV0dG9uVmFyaWFudHMgPSBjdmEoXHJcbiAgXCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ2FwLTIgd2hpdGVzcGFjZS1ub3dyYXAgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgdHJhbnNpdGlvbi1jb2xvcnMgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOnBvaW50ZXItZXZlbnRzLW5vbmUgZGlzYWJsZWQ6b3BhY2l0eS01MCBbJl9zdmddOnBvaW50ZXItZXZlbnRzLW5vbmUgWyZfc3ZnXTpzaXplLTQgWyZfc3ZnXTpzaHJpbmstMFwiLFxyXG4gIHtcclxuICAgIHZhcmlhbnRzOiB7XHJcbiAgICAgIHZhcmlhbnQ6IHtcclxuICAgICAgICBkZWZhdWx0OiBcImJnLXByaW1hcnkgdGV4dC1wcmltYXJ5LWZvcmVncm91bmQgaG92ZXI6YmctcHJpbWFyeS85MFwiLFxyXG4gICAgICAgIGRlc3RydWN0aXZlOlxyXG4gICAgICAgICAgXCJiZy1kZXN0cnVjdGl2ZSB0ZXh0LWRlc3RydWN0aXZlLWZvcmVncm91bmQgaG92ZXI6YmctZGVzdHJ1Y3RpdmUvOTBcIixcclxuICAgICAgICBvdXRsaW5lOlxyXG4gICAgICAgICAgXCJib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgaG92ZXI6YmctYWNjZW50IGhvdmVyOnRleHQtYWNjZW50LWZvcmVncm91bmRcIixcclxuICAgICAgICBzZWNvbmRhcnk6XHJcbiAgICAgICAgICBcImJnLXNlY29uZGFyeSB0ZXh0LXNlY29uZGFyeS1mb3JlZ3JvdW5kIGhvdmVyOmJnLXNlY29uZGFyeS84MFwiLFxyXG4gICAgICAgIGdob3N0OiBcImhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kXCIsXHJcbiAgICAgICAgbGluazogXCJ0ZXh0LXByaW1hcnkgdW5kZXJsaW5lLW9mZnNldC00IGhvdmVyOnVuZGVybGluZVwiLFxyXG4gICAgICB9LFxyXG4gICAgICBzaXplOiB7XHJcbiAgICAgICAgZGVmYXVsdDogXCJoLTEwIHB4LTQgcHktMlwiLFxyXG4gICAgICAgIHNtOiBcImgtOSByb3VuZGVkLW1kIHB4LTNcIixcclxuICAgICAgICBsZzogXCJoLTExIHJvdW5kZWQtbWQgcHgtOFwiLFxyXG4gICAgICAgIGljb246IFwiaC0xMCB3LTEwXCIsXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAgZGVmYXVsdFZhcmlhbnRzOiB7XHJcbiAgICAgIHZhcmlhbnQ6IFwiZGVmYXVsdFwiLFxyXG4gICAgICBzaXplOiBcImRlZmF1bHRcIixcclxuICAgIH0sXHJcbiAgfVxyXG4pXHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIEJ1dHRvblByb3BzXHJcbiAgZXh0ZW5kcyBSZWFjdC5CdXR0b25IVE1MQXR0cmlidXRlczxIVE1MQnV0dG9uRWxlbWVudD4sXHJcbiAgICBWYXJpYW50UHJvcHM8dHlwZW9mIGJ1dHRvblZhcmlhbnRzPiB7XHJcbiAgYXNDaGlsZD86IGJvb2xlYW5cclxufVxyXG5cclxuY29uc3QgQnV0dG9uID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MQnV0dG9uRWxlbWVudCwgQnV0dG9uUHJvcHM+KFxyXG4gICh7IGNsYXNzTmFtZSwgdmFyaWFudCwgc2l6ZSwgYXNDaGlsZCA9IGZhbHNlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcclxuICAgIGNvbnN0IENvbXAgPSBhc0NoaWxkID8gU2xvdCA6IFwiYnV0dG9uXCJcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxDb21wXHJcbiAgICAgICAgY2xhc3NOYW1lPXtjbihidXR0b25WYXJpYW50cyh7IHZhcmlhbnQsIHNpemUsIGNsYXNzTmFtZSB9KSl9XHJcbiAgICAgICAgcmVmPXtyZWZ9XHJcbiAgICAgICAgey4uLnByb3BzfVxyXG4gICAgICAvPlxyXG4gICAgKVxyXG4gIH1cclxuKVxyXG5CdXR0b24uZGlzcGxheU5hbWUgPSBcIkJ1dHRvblwiXHJcblxyXG5leHBvcnQgeyBCdXR0b24sIGJ1dHRvblZhcmlhbnRzIH1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU2xvdCIsImN2YSIsImNuIiwiYnV0dG9uVmFyaWFudHMiLCJ2YXJpYW50cyIsInZhcmlhbnQiLCJkZWZhdWx0IiwiZGVzdHJ1Y3RpdmUiLCJvdXRsaW5lIiwic2Vjb25kYXJ5IiwiZ2hvc3QiLCJsaW5rIiwic2l6ZSIsInNtIiwibGciLCJpY29uIiwiZGVmYXVsdFZhcmlhbnRzIiwiQnV0dG9uIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsImFzQ2hpbGQiLCJwcm9wcyIsInJlZiIsIkNvbXAiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dialog.tsx":
/*!**********************************!*\
  !*** ./components/ui/dialog.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogFooter: () => (/* binding */ DialogFooter),\n/* harmony export */   DialogHeader: () => (/* binding */ DialogHeader),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Dialog,DialogPortal,DialogOverlay,DialogClose,DialogTrigger,DialogContent,DialogHeader,DialogFooter,DialogTitle,DialogDescription auto */ \n\n\n\n\nconst Dialog = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DialogTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DialogPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DialogClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close;\nconst DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 21,\n        columnNumber: 3\n    }, undefined));\nDialogOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay.displayName;\nconst DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogOverlay, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\", className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 38,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nDialogContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DialogHeader = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined);\nDialogHeader.displayName = \"DialogHeader\";\nconst DialogFooter = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 74,\n        columnNumber: 3\n    }, undefined);\nDialogFooter.displayName = \"DialogFooter\";\nconst DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 88,\n        columnNumber: 3\n    }, undefined));\nDialogTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title.displayName;\nconst DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 103,\n        columnNumber: 3\n    }, undefined));\nDialogDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dialog.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dropdown-menu.tsx":
/*!*****************************************!*\
  !*** ./components/ui/dropdown-menu.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(ssr)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DropdownMenu,DropdownMenuTrigger,DropdownMenuContent,DropdownMenuItem,DropdownMenuCheckboxItem,DropdownMenuRadioItem,DropdownMenuLabel,DropdownMenuSeparator,DropdownMenuShortcut,DropdownMenuGroup,DropdownMenuPortal,DropdownMenuSub,DropdownMenuSubContent,DropdownMenuSubTrigger,DropdownMenuRadioGroup auto */ \n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"ml-auto\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n            lineNumber: 64,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 83,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, checked, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 108,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 99,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-2 w-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 131,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 123,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 147,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 163,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\nconst DropdownMenuShortcut = ({ className, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, undefined);\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFFaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGtZQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxjb21wb25lbnRzXFx1aVxcaW5wdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXHJcblxyXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXHJcblxyXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJpbnB1dFwiPj4oXHJcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxpbnB1dFxyXG4gICAgICAgIHR5cGU9e3R5cGV9XHJcbiAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1iYXNlIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIGZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTAgbWQ6dGV4dC1zbVwiLFxyXG4gICAgICAgICAgY2xhc3NOYW1lXHJcbiAgICAgICAgKX1cclxuICAgICAgICByZWY9e3JlZn1cclxuICAgICAgICB7Li4ucHJvcHN9XHJcbiAgICAgIC8+XHJcbiAgICApXHJcbiAgfVxyXG4pXHJcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXHJcblxyXG5leHBvcnQgeyBJbnB1dCB9XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFOEI7QUFDeUI7QUFDVTtBQUVqQztBQUVoQyxNQUFNSSxnQkFBZ0JGLDZEQUFHQSxDQUN2QjtBQUdGLE1BQU1HLHNCQUFRTCw2Q0FBZ0IsQ0FJNUIsQ0FBQyxFQUFFTyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNSLHVEQUFtQjtRQUNsQlEsS0FBS0E7UUFDTEYsV0FBV0osOENBQUVBLENBQUNDLGlCQUFpQkc7UUFDOUIsR0FBR0MsS0FBSzs7Ozs7O0FBR2JILE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbkMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXGNvbXBvbmVudHNcXHVpXFxsYWJlbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcclxuXHJcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXHJcbmltcG9ydCAqIGFzIExhYmVsUHJpbWl0aXZlIGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtbGFiZWxcIlxyXG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiXHJcblxyXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXHJcblxyXG5jb25zdCBsYWJlbFZhcmlhbnRzID0gY3ZhKFxyXG4gIFwidGV4dC1zbSBmb250LW1lZGl1bSBsZWFkaW5nLW5vbmUgcGVlci1kaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgcGVlci1kaXNhYmxlZDpvcGFjaXR5LTcwXCJcclxuKVxyXG5cclxuY29uc3QgTGFiZWwgPSBSZWFjdC5mb3J3YXJkUmVmPFxyXG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIExhYmVsUHJpbWl0aXZlLlJvb3Q+LFxyXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4gJlxyXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBsYWJlbFZhcmlhbnRzPlxyXG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXHJcbiAgPExhYmVsUHJpbWl0aXZlLlJvb3RcclxuICAgIHJlZj17cmVmfVxyXG4gICAgY2xhc3NOYW1lPXtjbihsYWJlbFZhcmlhbnRzKCksIGNsYXNzTmFtZSl9XHJcbiAgICB7Li4ucHJvcHN9XHJcbiAgLz5cclxuKSlcclxuTGFiZWwuZGlzcGxheU5hbWUgPSBMYWJlbFByaW1pdGl2ZS5Sb290LmRpc3BsYXlOYW1lXHJcblxyXG5leHBvcnQgeyBMYWJlbCB9XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhYmVsUHJpbWl0aXZlIiwiY3ZhIiwiY24iLCJsYWJlbFZhcmlhbnRzIiwiTGFiZWwiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJSb290IiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/tabs.tsx":
/*!********************************!*\
  !*** ./components/ui/tabs.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tabs: () => (/* binding */ Tabs),\n/* harmony export */   TabsContent: () => (/* binding */ TabsContent),\n/* harmony export */   TabsList: () => (/* binding */ TabsList),\n/* harmony export */   TabsTrigger: () => (/* binding */ TabsTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-tabs */ \"(ssr)/./node_modules/@radix-ui/react-tabs/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Tabs,TabsList,TabsTrigger,TabsContent auto */ \n\n\n\nconst Tabs = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst TabsList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.List, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined));\nTabsList.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.List.displayName;\nconst TabsTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 29,\n        columnNumber: 3\n    }, undefined));\nTabsTrigger.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Trigger.displayName;\nconst TabsContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Content, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 44,\n        columnNumber: 3\n    }, undefined));\nTabsContent.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/tabs.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/textarea.tsx":
/*!************************************!*\
  !*** ./components/ui/textarea.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Textarea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\textarea.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n});\nTextarea.displayName = \"Textarea\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3RleHRhcmVhLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFFaEMsTUFBTUUseUJBQVdGLDZDQUFnQixDQUcvQixDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzFCLHFCQUNFLDhEQUFDQztRQUNDSCxXQUFXSCw4Q0FBRUEsQ0FDWCxxVEFDQUc7UUFFRkUsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUNBSCxTQUFTTSxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcY29tcG9uZW50c1xcdWlcXHRleHRhcmVhLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxyXG5cclxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxyXG5cclxuY29uc3QgVGV4dGFyZWEgPSBSZWFjdC5mb3J3YXJkUmVmPFxyXG4gIEhUTUxUZXh0QXJlYUVsZW1lbnQsXHJcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJ0ZXh0YXJlYVwiPlxyXG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDx0ZXh0YXJlYVxyXG4gICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgIFwiZmxleCBtaW4taC1bODBweF0gdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LWJhc2UgcmluZy1vZmZzZXQtYmFja2dyb3VuZCBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwIG1kOnRleHQtc21cIixcclxuICAgICAgICBjbGFzc05hbWVcclxuICAgICAgKX1cclxuICAgICAgcmVmPXtyZWZ9XHJcbiAgICAgIHsuLi5wcm9wc31cclxuICAgIC8+XHJcbiAgKVxyXG59KVxyXG5UZXh0YXJlYS5kaXNwbGF5TmFtZSA9IFwiVGV4dGFyZWFcIlxyXG5cclxuZXhwb3J0IHsgVGV4dGFyZWEgfVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIlRleHRhcmVhIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwicmVmIiwidGV4dGFyZWEiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/textarea.tsx\n");

/***/ }),

/***/ "(ssr)/./components/upload-modal.tsx":
/*!*************************************!*\
  !*** ./components/upload-modal.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadModal: () => (/* binding */ UploadModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(ssr)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=File,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=File,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=File,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ UploadModal auto */ \n\n\n\n\n\n\n\n\nfunction UploadModal({ isOpen, setIsOpen }) {\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n    };\n    const handleDragLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setIsDragging(false);\n        if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\n            const newFiles = Array.from(e.dataTransfer.files);\n            setFiles((prev)=>[\n                    ...prev,\n                    ...newFiles\n                ]);\n        }\n    };\n    const handleFileChange = (e)=>{\n        if (e.target.files && e.target.files.length > 0) {\n            const newFiles = Array.from(e.target.files);\n            setFiles((prev)=>[\n                    ...prev,\n                    ...newFiles\n                ]);\n        }\n    };\n    const removeFile = (index)=>{\n        setFiles((prev)=>prev.filter((_, i)=>i !== index));\n    };\n    const triggerFileInput = ()=>{\n        fileInputRef.current?.click();\n    };\n    const handleUpload = async ()=>{\n        if (files.length === 0) return;\n        try {\n            // Use the documentApi.uploadDocument function instead of direct API call\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_6__.documentApi.uploadDocument(files[0]); // Upload first file only\n            if (response.message) {\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success(response.message);\n                // Store the file info in localStorage\n                localStorage.setItem(\"uploadedFiles\", JSON.stringify(files.map((file)=>({\n                        name: file.name,\n                        size: file.size,\n                        type: file.type,\n                        lastModified: file.lastModified\n                    }))));\n                setIsOpen(false);\n                router.push(\"/process?type=upload\");\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(error.response?.data?.error || \"Failed to upload file. Please try again.\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: isOpen,\n        onOpenChange: setIsOpen,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"sm:max-w-md bg-neutral-900 border-neutral-800\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                        className: \"text-center\",\n                        children: \"Upload Files\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `mt-4 border-2 border-dashed rounded-lg p-8 text-center transition-colors ${isDragging ? \"border-purple-500 bg-purple-500/10\" : \"border-neutral-700\"}`,\n                    onDragOver: handleDragOver,\n                    onDragLeave: handleDragLeave,\n                    onDrop: handleDrop,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"file\",\n                            ref: fileInputRef,\n                            onChange: handleFileChange,\n                            className: \"hidden\",\n                            multiple: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                scale: 1\n                            },\n                            animate: {\n                                scale: isDragging ? 1.05 : 1\n                            },\n                            className: \"flex flex-col items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-neutral-800 rounded-full mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-6 w-6 text-purple-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-neutral-300 mb-2\",\n                                    children: \"Drag and drop files here\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-neutral-500 text-sm mb-4\",\n                                    children: \"or\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: triggerFileInput,\n                                    className: \"bg-purple-600 hover:bg-purple-700\",\n                                    children: \"Browse Files\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-neutral-500 text-xs mt-4\",\n                                    children: \"Supported formats: PDF, PPT, DOC, TXT, MP3, WAV\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this),\n                files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium mb-2\",\n                            children: \"Selected Files\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-h-40 overflow-y-auto space-y-2\",\n                            children: files.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between bg-neutral-800 p-2 rounded\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 text-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm truncate max-w-[200px]\",\n                                                    children: file.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            className: \"h-6 w-6\",\n                                            onClick: ()=>removeFile(index),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex justify-end gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setFiles([]),\n                                    children: \"Clear All\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    className: \"bg-purple-600 hover:bg-purple-700\",\n                                    disabled: files.length === 0,\n                                    onClick: handleUpload,\n                                    children: [\n                                        \"Upload \",\n                                        files.length,\n                                        \" \",\n                                        files.length === 1 ? \"File\" : \"Files\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\cogni_api\\\\frontend\\\\components\\\\upload-modal.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/upload-modal.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   chatApi: () => (/* binding */ chatApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   documentApi: () => (/* binding */ documentApi),\n/* harmony export */   generateFlashcards: () => (/* binding */ generateFlashcards),\n/* harmony export */   generateFlowchart: () => (/* binding */ generateFlowchart),\n/* harmony export */   generateQuiz: () => (/* binding */ generateQuiz),\n/* harmony export */   performanceApi: () => (/* binding */ performanceApi),\n/* harmony export */   processBlueprint: () => (/* binding */ processBlueprint)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// Base URL for API requests\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';\n// Create axios instance with default config\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// Add request interceptor to include auth token\napi.interceptors.request.use((config)=>{\n    const token = localStorage.getItem('token');\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n});\n// Auth API endpoints\nconst authApi = {\n    signIn: async (credentials)=>{\n        try {\n            const response = await api.post('/users/login/', credentials);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    register: async (userData)=>{\n        try {\n            const response = await api.post('/users/register/', userData);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    verifyOTP: async (data)=>{\n        try {\n            const response = await api.post('/users/verify-otp/', data);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    logout: async ()=>{\n        try {\n            const response = await api.post('/users/logout/');\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n};\n// Document API endpoints\nconst documentApi = {\n    uploadDocument: async (file)=>{\n        const formData = new FormData();\n        formData.append('file', file);\n        try {\n            const response = await api.post('/documents/upload/', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    getDocuments: async ()=>{\n        try {\n            const response = await api.get('/documents/');\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    getDocumentStatus: async (documentId)=>{\n        try {\n            const response = await api.get(`/documents/${documentId}/`);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n};\n// Chat API endpoints\nconst chatApi = {\n    sendMessage: async (message, documentId)=>{\n        try {\n            const response = await api.post('/chat/message/', {\n                message,\n                document_id: documentId\n            });\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    getHistory: async ()=>{\n        try {\n            const response = await api.get('/chat/history/');\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n};\n// Performance API endpoints\nconst performanceApi = {\n    createPerformance: async (performanceData)=>{\n        try {\n            const response = await api.post('/users/performance/', performanceData);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    getPerformances: async (documentId)=>{\n        try {\n            const url = documentId ? `/users/performance/?document=${documentId}` : '/users/performance/';\n            const response = await api.get(url);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    getStudentPerformances: async (studentId)=>{\n        try {\n            const response = await api.get(`/users/performance/?student=${studentId}`);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    getPerformanceStats: async (documentId)=>{\n        try {\n            const response = await api.get(`/users/performance/?document=${documentId}`);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n};\n// Blueprint API (process blueprint)\nconst processBlueprint = async (documentId, blueprintText, llmModel = 'openai' // can be 'gemini', 'rag', 'openai', etc.\n)=>{\n    try {\n        const response = await api.post(`/process-blueprint/${documentId}/`, {\n            document_id: documentId,\n            blueprint_text: blueprintText,\n            llm_model: llmModel\n        });\n        return response.data;\n    } catch (error) {\n        console.error('Error processing blueprint:', error);\n        throw error;\n    }\n};\n// Flashcards API\nconst generateFlashcards = async (documentId, numFlashcards = 10, llmModel = 'openai')=>{\n    try {\n        const response = await api.post(`/flashcards/${documentId}/`, {\n            num_flashcards: numFlashcards,\n            llm_model: llmModel\n        });\n        return response.data;\n    } catch (error) {\n        console.error('Error generating flashcards:', error);\n        throw error;\n    }\n};\n// Flowchart API\nconst generateFlowchart = async (documentId, llmModel = 'openai')=>{\n    try {\n        const response = await api.post(`/flowchart/${documentId}/`, {\n            llm_model: llmModel\n        });\n        return response.data;\n    } catch (error) {\n        console.error('Error generating flowchart:', error);\n        throw error;\n    }\n};\n// Quiz API\nconst generateQuiz = async (documentId, numQuestions = 5, llmModel = 'openai')=>{\n    try {\n        const response = await api.post(`/quizzes/${documentId}/`, {\n            num_questions: numQuestions,\n            llm_model: llmModel\n        });\n        return response.data;\n    } catch (error) {\n        console.error('Error generating quiz:', error);\n        throw error;\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * Combines multiple class names into a single string, merging Tailwind CSS classes properly\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFeEM7O0NBRUMsR0FDTSxTQUFTRSxHQUFHLEdBQUdDLE1BQW9CO0lBQ3hDLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbi8qKlxuICogQ29tYmluZXMgbXVsdGlwbGUgY2xhc3MgbmFtZXMgaW50byBhIHNpbmdsZSBzdHJpbmcsIG1lcmdpbmcgVGFpbHdpbmQgQ1NTIGNsYXNzZXMgcHJvcGVybHlcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Ccomponents%5C%5Ccogni-ui.tsx%22%2C%22ids%22%3A%5B%22CogniUI%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Ccomponents%5C%5Ccogni-ui.tsx%22%2C%22ids%22%3A%5B%22CogniUI%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/cogni-ui.tsx */ \"(ssr)/./components/cogni-ui.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NhYW5kJTVDJTVDT25lRHJpdmUlNUMlNUNEb2N1bWVudHMlNUMlNUNjb2duaV9hcGklNUMlNUNmcm9udGVuZCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjb2duaS11aS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJDb2duaVVJJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4SkFBc0oiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkNvZ25pVUlcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxzYWFuZFxcXFxPbmVEcml2ZVxcXFxEb2N1bWVudHNcXFxcY29nbmlfYXBpXFxcXGZyb250ZW5kXFxcXGNvbXBvbmVudHNcXFxcY29nbmktdWkudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Ccomponents%5C%5Ccogni-ui.tsx%22%2C%22ids%22%3A%5B%22CogniUI%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csaand%5C%5COneDrive%5C%5CDocuments%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?4c03":
/*!***********************!*\
  !*** debug (ignored) ***!
  \***********************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/sonner","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/axios","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/motion-utils","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/react-remove-scroll","vendor-chunks/es-errors","vendor-chunks/@floating-ui","vendor-chunks/call-bind-apply-helpers","vendor-chunks/react-style-singleton","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/get-proto","vendor-chunks/use-sidecar","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/tslib","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority","vendor-chunks/proxy-from-env","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/get-nonce","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream","vendor-chunks/aria-hidden"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Csaand%5COneDrive%5CDocuments%5Ccogni_api%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csaand%5COneDrive%5CDocuments%5Ccogni_api%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
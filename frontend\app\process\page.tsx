"use client"

import { useState, useEffect, useRef } from "react"
import { useSearchParams } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable"
import {
  MessageSquare,
  FileText,
  FileLineChartIcon as FlowChart,
  BookOpen,
  HelpCircle,
  BarChart4,
  FlashlightIcon as Flashcard,
} from "lucide-react"
import { UploadContent } from "@/components/upload-content"
import { PasteContent } from "@/components/paste-content"
import { RecordContent } from "@/components/record-content"
import { ChatInterface } from "@/components/chat-interface"
import { QuizInterface } from "@/components/quiz-interface"
import { PerformanceDashboard } from "@/components/performance-dashboard"
import { motion } from "framer-motion"
import { LayoutWithSidebar } from "@/components/layout-with-sidebar"
import { useTheme } from "@/components/theme-provider"
import { BlueprintInterface } from "@/components/blueprint-interface"
import { QuizzesInterface } from "@/components/quizzes-interface"
import { FlowchartInterface } from "@/components/flowchart-interface"
import { FlashcardsInterface } from "@/components/flashcards-interface"

export default function ProcessPage() {
  const searchParams = useSearchParams()
  const type = searchParams.get("type") || "upload"
  const [activeTab, setActiveTab] = useState("chat")
  const [showFullScreen, setShowFullScreen] = useState(true)
  const [uploadedData, setUploadedData] = useState<any>(null)
  const { theme } = useTheme()
  const tabsListRef = useRef<HTMLDivElement>(null)

  // State preservation
  const [chatState, setChatState] = useState([])
  const [quizState, setQuizState] = useState({})
  const [summaryState, setSummaryState] = useState("")
  const [flowchartState, setFlowchartState] = useState("")
  const [flashcardState, setFlashcardState] = useState("")
  const [chapterState, setChapterState] = useState("")
  const [transcriptState, setTranscriptState] = useState("")

  useEffect(() => {
    document.body.classList.add("page-transition")

    const uploadedFilesStr = localStorage.getItem("uploadedFiles")
    const filePreviewUrlsStr = localStorage.getItem("filePreviewUrls")

    if (uploadedFilesStr && type === "upload") {
      try {
        const uploadedFiles = JSON.parse(uploadedFilesStr)
        if (uploadedFiles.length > 0) {
          const file = uploadedFiles[0]
          let previewUrl = null
          if (filePreviewUrlsStr) {
            const urls = JSON.parse(filePreviewUrlsStr)
            if (urls.length > 0) {
              previewUrl = urls[0]
            }
          }
          setUploadedData({
            type: "file",
            name: file.name,
            size: file.size,
            previewUrl: previewUrl,
          })
          setShowFullScreen(false)
        }
      } catch (error) {
        console.error("Error parsing uploaded files:", error)
      }
    } else if (type === "paste") {
      const pastedContentStr = localStorage.getItem("pastedContent")
      if (pastedContentStr) {
        try {
          const pastedContent = JSON.parse(pastedContentStr)
          setUploadedData({
            type: "text",
            content: pastedContent.content || pastedContent.url || "Pasted content",
          })
          setShowFullScreen(false)
        } catch (error) {
          console.error("Error parsing pasted content:", error)
        }
      }
    } else if (type === "record") {
      const recordedAudioStr = localStorage.getItem("recordedAudio")
      if (recordedAudioStr) {
        try {
          const recordedAudio = JSON.parse(recordedAudioStr)
          setUploadedData({
            type: "audio",
            name: "Recording-" + new Date().toISOString().split("T")[0] + ".wav",
            duration: recordedAudio.duration || "00:00",
          })
          setShowFullScreen(false)
        } catch (error) {
          console.error("Error parsing recorded audio:", error)
        }
      }
    }

    return () => {
      document.body.classList.remove("page-transition")
    }
  }, [type])

  const renderInputComponent = () => {
    if (uploadedData) {
      if (uploadedData.type === "file") {
        return (
          <div className="p-4 h-full flex flex-col">
            <h2 className="text-xl font-semibold mb-4">{uploadedData.name}</h2>
            <div className="flex-1 overflow-auto">
              <div className={`rounded-lg flex flex-col items-center justify-center ${theme === "light" ? "bg-white border border-black" : "bg-neutral-800"}`}>
                {uploadedData.previewUrl && uploadedData.name.toLowerCase().endsWith('.pdf') ? (
                  <iframe
                    src={uploadedData.previewUrl}
                    title={uploadedData.name}
                    width="100%"
                    height="600px"
                    style={{ border: 'none', borderRadius: '12px' }}
                  />
                ) : uploadedData.previewUrl && uploadedData.name.toLowerCase().match(/\.(jpg|jpeg|png|gif)$/) ? (
                  <img src={uploadedData.previewUrl} alt={uploadedData.name} className="max-w-full object-contain" />
                ) : (
                  <div className="text-center p-8">
                    <p className="text-lg">{uploadedData.name}</p>
                    <p className={`text-sm ${theme === "light" ? "text-neutral-600" : "text-neutral-500"}`}>{uploadedData.size}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )
      } else if (uploadedData.type === "text") {
        return (
          <div className="p-4 h-full flex flex-col">
            <h2 className="text-xl font-semibold mb-4">Pasted Content</h2>
            <div className="flex-1 overflow-auto">
              <div className={`rounded-lg p-4 ${theme === "light" ? "bg-white border border-black" : "bg-neutral-800"}`}>
                <p>{uploadedData.content}</p>
              </div>
            </div>
          </div>
        )
      } else if (uploadedData.type === "audio") {
        return (
          <div className="p-4">
            <h2 className="text-xl font-semibold mb-4">{uploadedData.name}</h2>
            <div className={`rounded-lg p-4 flex flex-col items-center ${
              theme === "light" ? "bg-white border border-black" : "bg-neutral-800"
            }`}>
              <div className={`w-full h-24 rounded-md mb-4 ${theme === "light" ? "bg-gray-200" : "bg-neutral-700"}`} />
              <audio controls className="w-full">
                <source src="#" type="audio/wav" />
              </audio>
              <p className={`text-sm mt-2 ${theme === "light" ? "text-neutral-600" : "text-neutral-500"}`}>
                Duration: {uploadedData.duration}
              </p>
            </div>
          </div>
        )
      }
    }

    switch (type) {
      case "upload": return <UploadContent />
      case "paste": return <PasteContent />
      case "record": return <RecordContent />
      default: return <UploadContent />
    }
  }

  const renderOutputComponent = () => {
    const documentId = searchParams.get("documentId") ? parseInt(searchParams.get("documentId")!) : undefined

    switch (activeTab) {
      case "chat":
        return <ChatInterface state={chatState} setState={setChatState} />;
      case "summary":
        return <div className="p-4">{summaryState || "Summary content will appear here"}</div>;
      case "flowchart":
        return <FlowchartInterface />;
      case "flashcards":
        return <FlashcardsInterface />;
      case "quizzes":
        return <QuizInterface documentId={documentId} />;
      case "chapters":
        return <div className="p-4">{chapterState || "Chapters content will appear here"}</div>;
      case "transcript":
        return <PerformanceDashboard documentId={documentId} />;
      case "blueprint":
        return <BlueprintInterface />;
      default:
        return <ChatInterface state={chatState} setState={setChatState} />;

    }
  }

  return (
    <LayoutWithSidebar>
      <motion.div
        className="h-[calc(100vh-65px)] overflow-hidden bg-background text-foreground flex flex-col"
        initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3 }}
      >
        {showFullScreen ? (
          <motion.div className="flex-1 p-4">{renderInputComponent()}</motion.div>
        ) : (
          <ResizablePanelGroup direction="horizontal" className="flex-1 overflow-hidden">
            <ResizablePanel defaultSize={40} minSize={30}>
              <div className="h-full overflow-auto">{renderInputComponent()}</div>
            </ResizablePanel>
            <ResizableHandle withHandle />
            <ResizablePanel defaultSize={60} minSize={30}>
              <div className="h-full flex flex-col">
                <Tabs defaultValue="chat" onValueChange={setActiveTab} value={activeTab} className="w-full h-full">
                  <div className="border-b border-border sticky top-0 bg-background z-10">
                    <div className="px-4 overflow-x-auto scrollbar-hide" ref={tabsListRef}>
                      <TabsList className="h-12 w-max">
                        <TabsTrigger value="chat" className="gap-2">
                          <MessageSquare className="h-4 w-4" />Chat
                        </TabsTrigger>
                        <TabsTrigger value="blueprint" className="gap-2">
                          <FlowChart className="h-4 w-4" />Blueprint
                        </TabsTrigger>
                        <TabsTrigger value="summary" className="gap-2">
                          <FileText className="h-4 w-4" />Summary
                        </TabsTrigger>
                        <TabsTrigger value="flowchart" className="gap-2">
                          <FlowChart className="h-4 w-4" />Flowchart
                        </TabsTrigger>
                        <TabsTrigger value="flashcards" className="gap-2">
                          <Flashcard className="h-4 w-4" />Flashcards
                        </TabsTrigger>
                        <TabsTrigger value="quizzes" className="gap-2">
                          <HelpCircle className="h-4 w-4" />Quizzes
                        </TabsTrigger>
                        <TabsTrigger value="chapters" className="gap-2">
                          <BookOpen className="h-4 w-4" />Chapters
                        </TabsTrigger>
                        <TabsTrigger value="transcript" className="gap-2">
                          <BarChart4 className="h-4 w-4" />Performance
                        </TabsTrigger>
                      </TabsList>
                    </div>
                  </div>
                </Tabs>

                      </TabsList>
                    </div>
                  </div>

                  <TabsContent value={activeTab} className="flex-1 p-0 m-0 overflow-hidden h-full">
                    <div className="h-full overflow-y-auto pb-8 scrollbar-thin scrollbar-thumb-neutral-500 scrollbar-track-transparent hover:scrollbar-thumb-neutral-400">{renderOutputComponent()}</div>
                  </TabsContent>
                </Tabs>
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        )}
      </motion.div>
    </LayoutWithSidebar>
  )
}
